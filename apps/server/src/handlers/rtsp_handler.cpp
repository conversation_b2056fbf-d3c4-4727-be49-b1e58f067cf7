#include "handlers/rtsp_handler.hpp"
#include "shared/config.hpp"
#include <iostream>
#include <regex>

namespace server::handlers {

RTSPHandler::RTSPHandler(std::shared_ptr<shared::ServerConfig> config,
                         std::shared_ptr<aibox::rtsp::StreamMultiplexer> multiplexer)
    : BaseHandler(config), multiplexer_(multiplexer) {
    logInfo("RTSPHandler initialized", "RTSPHandler");
}

utils::JsonValue RTSPHandler::handleAddStream(const utils::JsonValue& request) {
    try {
        // Parse and validate stream configuration
        auto stream_config = parseStreamConfig(request);
        if (!validateStreamConfig(stream_config)) {
            return createInvalidConfigError("Invalid stream configuration");
        }

        // Check if stream already exists
        auto existing_streams = multiplexer_->getStreamIds();
        for (const auto& id : existing_streams) {
            if (id == stream_config.stream_id) {
                return createErrorResponse("Stream already exists", "STREAM_EXISTS", 409);
            }
        }

        // Add stream to multiplexer
        bool success = multiplexer_->addStream(stream_config.stream_id, stream_config);
        if (!success) {
            return createSystemError("Failed to add stream to multiplexer");
        }

        // Return success response with stream info
        auto stream_info = multiplexer_->getStreamInfo(stream_config.stream_id);
        utils::JsonValue data = streamInfoToJson(stream_info);
        
        return createSuccessResponse("Stream added successfully", data);

    } catch (const std::exception& e) {
        logError("Exception in handleAddStream: " + std::string(e.what()), "RTSPHandler");
        return createSystemError("Internal error while adding stream");
    }
}

utils::JsonValue RTSPHandler::handleGetStreams() {
    try {
        auto stream_ids = multiplexer_->getStreamIds();
        auto stream_infos = multiplexer_->getStreamInfos();

        utils::JsonArray streams_array;
        for (const auto& info : stream_infos) {
            streams_array.push_back(streamInfoToJson(info));
        }

        utils::JsonObject data;
        data["streams"] = utils::JsonValue(streams_array);
        data["total_count"] = utils::JsonValue(static_cast<int>(stream_ids.size()));
        data["active_count"] = utils::JsonValue(static_cast<int>(multiplexer_->getActiveStreamCount()));

        return createSuccessResponse("Streams retrieved successfully", utils::JsonValue(data));

    } catch (const std::exception& e) {
        logError("Exception in handleGetStreams: " + std::string(e.what()), "RTSPHandler");
        return createSystemError("Internal error while retrieving streams");
    }
}

utils::JsonValue RTSPHandler::handleGetStream(const std::string& stream_id) {
    try {
        if (!validateStreamId(stream_id)) {
            return createErrorResponse("Invalid stream ID format", "INVALID_STREAM_ID", 400);
        }

        // Check if stream exists
        auto stream_ids = multiplexer_->getStreamIds();
        bool found = std::find(stream_ids.begin(), stream_ids.end(), stream_id) != stream_ids.end();
        if (!found) {
            return createStreamNotFoundError(stream_id);
        }

        // Get stream info
        auto stream_info = multiplexer_->getStreamInfo(stream_id);
        utils::JsonValue data = streamInfoToJson(stream_info);

        return createSuccessResponse("Stream retrieved successfully", data);

    } catch (const std::exception& e) {
        logError("Exception in handleGetStream: " + std::string(e.what()), "RTSPHandler");
        return createSystemError("Internal error while retrieving stream");
    }
}

utils::JsonValue RTSPHandler::handleUpdateStream(const std::string& stream_id, const utils::JsonValue& request) {
    try {
        if (!validateStreamId(stream_id)) {
            return createErrorResponse("Invalid stream ID format", "INVALID_STREAM_ID", 400);
        }

        // Check if stream exists
        auto stream_ids = multiplexer_->getStreamIds();
        bool found = std::find(stream_ids.begin(), stream_ids.end(), stream_id) != stream_ids.end();
        if (!found) {
            return createStreamNotFoundError(stream_id);
        }

        // Parse new configuration
        auto new_config = parseStreamConfig(request);
        new_config.stream_id = stream_id; // Ensure ID matches URL parameter
        
        if (!validateStreamConfig(new_config)) {
            return createInvalidConfigError("Invalid stream configuration");
        }

        // Update stream configuration
        bool success = multiplexer_->updateStreamConfig(stream_id, new_config);
        if (!success) {
            return createSystemError("Failed to update stream configuration");
        }

        // Return updated stream info
        auto stream_info = multiplexer_->getStreamInfo(stream_id);
        utils::JsonValue data = streamInfoToJson(stream_info);

        return createSuccessResponse("Stream updated successfully", data);

    } catch (const std::exception& e) {
        logError("Exception in handleUpdateStream: " + std::string(e.what()), "RTSPHandler");
        return createSystemError("Internal error while updating stream");
    }
}

utils::JsonValue RTSPHandler::handleRemoveStream(const std::string& stream_id) {
    try {
        if (!validateStreamId(stream_id)) {
            return createErrorResponse("Invalid stream ID format", "INVALID_STREAM_ID", 400);
        }

        // Check if stream exists
        auto stream_ids = multiplexer_->getStreamIds();
        bool found = std::find(stream_ids.begin(), stream_ids.end(), stream_id) != stream_ids.end();
        if (!found) {
            return createStreamNotFoundError(stream_id);
        }

        // Remove stream
        bool success = multiplexer_->removeStream(stream_id);
        if (!success) {
            return createSystemError("Failed to remove stream");
        }

        utils::JsonObject data;
        data["stream_id"] = utils::JsonValue(stream_id);
        data["removed"] = utils::JsonValue(true);

        return createSuccessResponse("Stream removed successfully", utils::JsonValue(data));

    } catch (const std::exception& e) {
        logError("Exception in handleRemoveStream: " + std::string(e.what()), "RTSPHandler");
        return createSystemError("Internal error while removing stream");
    }
}

utils::JsonValue RTSPHandler::handleGetStreamStats(const std::string& stream_id) {
    try {
        if (!validateStreamId(stream_id)) {
            return createErrorResponse("Invalid stream ID format", "INVALID_STREAM_ID", 400);
        }

        // Check if stream exists
        auto stream_ids = multiplexer_->getStreamIds();
        bool found = std::find(stream_ids.begin(), stream_ids.end(), stream_id) != stream_ids.end();
        if (!found) {
            return createStreamNotFoundError(stream_id);
        }

        // Get stream info which includes statistics
        auto stream_info = multiplexer_->getStreamInfo(stream_id);
        utils::JsonValue data = streamStatsToJson(stream_info.statistics);

        return createSuccessResponse("Stream statistics retrieved successfully", data);

    } catch (const std::exception& e) {
        logError("Exception in handleGetStreamStats: " + std::string(e.what()), "RTSPHandler");
        return createSystemError("Internal error while retrieving stream statistics");
    }
}

utils::JsonValue RTSPHandler::handleControlStream(const std::string& stream_id, const utils::JsonValue& request) {
    try {
        if (!validateStreamId(stream_id)) {
            return createErrorResponse("Invalid stream ID format", "INVALID_STREAM_ID", 400);
        }

        // Check if stream exists
        auto stream_ids = multiplexer_->getStreamIds();
        bool found = std::find(stream_ids.begin(), stream_ids.end(), stream_id) != stream_ids.end();
        if (!found) {
            return createStreamNotFoundError(stream_id);
        }

        // Extract action from request
        std::string action = extractStringField(request, "action");
        if (!validateControlAction(action)) {
            return createErrorResponse("Invalid control action", "INVALID_ACTION", 400);
        }

        bool success = false;
        std::string result_message;

        // Execute control action
        if (action == "start" || action == "connect") {
            success = multiplexer_->connectStream(stream_id);
            result_message = success ? "Stream started successfully" : "Failed to start stream";
        } else if (action == "stop" || action == "disconnect") {
            success = multiplexer_->disconnectStream(stream_id);
            result_message = success ? "Stream stopped successfully" : "Failed to stop stream";
        } else if (action == "restart" || action == "reconnect") {
            success = multiplexer_->reconnectStream(stream_id);
            result_message = success ? "Stream restarted successfully" : "Failed to restart stream";
        }

        if (!success) {
            return createSystemError("Control action failed: " + result_message);
        }

        // Return updated stream info
        auto stream_info = multiplexer_->getStreamInfo(stream_id);
        utils::JsonObject data;
        data["stream_id"] = utils::JsonValue(stream_id);
        data["action"] = utils::JsonValue(action);
        data["success"] = utils::JsonValue(success);
        data["stream_info"] = streamInfoToJson(stream_info);

        return createSuccessResponse(result_message, utils::JsonValue(data));

    } catch (const std::exception& e) {
        logError("Exception in handleControlStream: " + std::string(e.what()), "RTSPHandler");
        return createSystemError("Internal error while controlling stream");
    }
}

utils::JsonValue RTSPHandler::handleGetSystemStatus() {
    try {
        auto health = multiplexer_->getSystemHealth();
        utils::JsonValue data = systemHealthToJson(health);
        return createSuccessResponse("System status retrieved successfully", data);

    } catch (const std::exception& e) {
        logError("Exception in handleGetSystemStatus: " + std::string(e.what()), "RTSPHandler");
        return createSystemError("Internal error while retrieving system status");
    }
}

utils::JsonValue RTSPHandler::handleGetSystemStats() {
    try {
        auto stats = multiplexer_->getStatistics();
        utils::JsonValue data = systemStatsToJson(stats);
        return createSuccessResponse("System statistics retrieved successfully", data);

    } catch (const std::exception& e) {
        logError("Exception in handleGetSystemStats: " + std::string(e.what()), "RTSPHandler");
        return createSystemError("Internal error while retrieving system statistics");
    }
}

// Helper methods implementation
aibox::rtsp::RTSPConnectionConfig RTSPHandler::parseStreamConfig(const utils::JsonValue& json) {
    aibox::rtsp::RTSPConnectionConfig config;

    // Required fields
    config.stream_id = extractStringField(json, "stream_id");
    config.rtsp_url = extractStringField(json, "rtsp_url");

    // Optional authentication
    config.username = extractStringField(json, "username");
    config.password = extractStringField(json, "password");

    // Optional network settings
    if (auto timeout = extractIntField(json, "timeout_ms")) {
        config.timeout_ms = *timeout;
    }
    if (auto retry_count = extractIntField(json, "retry_count")) {
        config.retry_count = *retry_count;
    }

    // Optional transport protocol
    std::string transport = extractStringField(json, "transport", "tcp");
    if (transport == "udp") {
        config.transport = aibox::rtsp::TransportProtocol::UDP;
    } else if (transport == "tcp") {
        config.transport = aibox::rtsp::TransportProtocol::TCP;
    } else {
        config.transport = aibox::rtsp::TransportProtocol::AUTO;
    }

    // Optional priority
    std::string priority = extractStringField(json, "priority", "medium");
    if (priority == "low") {
        config.priority = aibox::rtsp::StreamPriority::LOW;
    } else if (priority == "high") {
        config.priority = aibox::rtsp::StreamPriority::HIGH;
    } else if (priority == "critical") {
        config.priority = aibox::rtsp::StreamPriority::CRITICAL;
    } else {
        config.priority = aibox::rtsp::StreamPriority::MEDIUM;
    }

    // Optional enabled flag
    if (auto enabled = extractBoolField(json, "enabled")) {
        config.enabled = *enabled;
    }

    // Optional hardware acceleration settings
    if (auto use_mpp = extractBoolField(json, "use_mpp_decoder")) {
        config.use_mpp_decoder = *use_mpp;
    }
    if (auto use_rga = extractBoolField(json, "use_rga_scaler")) {
        config.use_rga_scaler = *use_rga;
    }
    if (auto use_dmabuf = extractBoolField(json, "use_dmabuf_zerocopy")) {
        config.use_dmabuf_zerocopy = *use_dmabuf;
    }

    return config;
}

utils::JsonValue RTSPHandler::streamConfigToJson(const aibox::rtsp::RTSPConnectionConfig& config) {
    utils::JsonObject obj;

    obj["stream_id"] = utils::JsonValue(config.stream_id);
    obj["rtsp_url"] = utils::JsonValue(config.rtsp_url);
    obj["username"] = utils::JsonValue(config.username);
    obj["enabled"] = utils::JsonValue(config.enabled);
    obj["timeout_ms"] = utils::JsonValue(config.timeout_ms);
    obj["retry_count"] = utils::JsonValue(config.retry_count);

    // Transport protocol
    std::string transport;
    switch (config.transport) {
        case aibox::rtsp::TransportProtocol::UDP: transport = "udp"; break;
        case aibox::rtsp::TransportProtocol::TCP: transport = "tcp"; break;
        case aibox::rtsp::TransportProtocol::AUTO: transport = "auto"; break;
    }
    obj["transport"] = utils::JsonValue(transport);

    // Priority
    std::string priority;
    switch (config.priority) {
        case aibox::rtsp::StreamPriority::LOW: priority = "low"; break;
        case aibox::rtsp::StreamPriority::MEDIUM: priority = "medium"; break;
        case aibox::rtsp::StreamPriority::HIGH: priority = "high"; break;
        case aibox::rtsp::StreamPriority::CRITICAL: priority = "critical"; break;
    }
    obj["priority"] = utils::JsonValue(priority);

    // Hardware acceleration
    obj["use_mpp_decoder"] = utils::JsonValue(config.use_mpp_decoder);
    obj["use_rga_scaler"] = utils::JsonValue(config.use_rga_scaler);
    obj["use_dmabuf_zerocopy"] = utils::JsonValue(config.use_dmabuf_zerocopy);

    return utils::JsonValue(obj);
}

utils::JsonValue RTSPHandler::streamInfoToJson(const aibox::rtsp::StreamInfo& info) {
    utils::JsonObject obj;

    obj["stream_id"] = utils::JsonValue(info.stream_id);
    obj["config"] = streamConfigToJson(info.config);

    // Connection state
    std::string state;
    switch (info.state) {
        case aibox::rtsp::ConnectionState::DISCONNECTED: state = "disconnected"; break;
        case aibox::rtsp::ConnectionState::CONNECTING: state = "connecting"; break;
        case aibox::rtsp::ConnectionState::CONNECTED: state = "connected"; break;
        case aibox::rtsp::ConnectionState::STREAMING: state = "streaming"; break;
        case aibox::rtsp::ConnectionState::ERROR: state = "error"; break;
        case aibox::rtsp::ConnectionState::RECONNECTING: state = "reconnecting"; break;
    }
    obj["state"] = utils::JsonValue(state);

    // Basic stream statistics from StreamInfo
    utils::JsonObject stats_obj;
    stats_obj["queue_depth"] = utils::JsonValue(static_cast<int64_t>(info.queue_depth));
    stats_obj["packets_processed"] = utils::JsonValue(static_cast<int64_t>(info.packets_processed));
    stats_obj["error_count"] = utils::JsonValue(static_cast<int>(info.error_count));
    stats_obj["enabled"] = utils::JsonValue(info.enabled);
    obj["statistics"] = utils::JsonValue(stats_obj);

    // Timestamps
    auto now = std::chrono::system_clock::now();
    obj["last_update"] = createTimestamp(now);

    return utils::JsonValue(obj);
}

utils::JsonValue RTSPHandler::streamStatsToJson(const aibox::rtsp::StreamStatistics& stats) {
    utils::JsonObject obj;

    // Connection stats
    obj["packets_received"] = utils::JsonValue(static_cast<int64_t>(stats.packets_received));
    obj["packets_lost"] = utils::JsonValue(static_cast<int64_t>(stats.packets_lost));
    obj["bytes_received"] = utils::JsonValue(static_cast<int64_t>(stats.bytes_received));
    obj["packet_loss_rate"] = utils::JsonValue(stats.getPacketLossRate());

    // Performance stats
    obj["current_fps"] = utils::JsonValue(static_cast<int>(stats.current_fps));
    obj["current_bitrate_kbps"] = utils::JsonValue(static_cast<int>(stats.current_bitrate_kbps));
    obj["average_latency_ms"] = utils::JsonValue(static_cast<int>(stats.average_latency_ms));

    // Hardware acceleration stats
    obj["mpp_decode_count"] = utils::JsonValue(static_cast<int>(stats.mpp_decode_count));
    obj["rga_process_count"] = utils::JsonValue(static_cast<int>(stats.rga_process_count));
    obj["software_fallback_count"] = utils::JsonValue(static_cast<int>(stats.software_fallback_count));
    obj["hardware_accel_rate"] = utils::JsonValue(stats.getHardwareAccelRate());

    // Resource usage
    obj["memory_usage_bytes"] = utils::JsonValue(static_cast<int64_t>(stats.memory_usage_bytes));
    obj["cpu_usage_percent"] = utils::JsonValue(static_cast<int>(stats.cpu_usage_percent));

    // Error stats
    obj["network_errors"] = utils::JsonValue(static_cast<int>(stats.network_errors));
    obj["decode_errors"] = utils::JsonValue(static_cast<int>(stats.decode_errors));
    obj["hardware_errors"] = utils::JsonValue(static_cast<int>(stats.hardware_errors));
    obj["reconnect_count"] = utils::JsonValue(static_cast<int>(stats.reconnect_count));

    return utils::JsonValue(obj);
}

utils::JsonValue RTSPHandler::systemHealthToJson(const aibox::rtsp::MultiplexerHealth& health) {
    utils::JsonObject obj;

    obj["status"] = utils::JsonValue(health.status);
    obj["score"] = utils::JsonValue(health.score);
    obj["active_streams"] = utils::JsonValue(static_cast<int>(health.active_streams));
    obj["error_count"] = utils::JsonValue(static_cast<int>(health.error_count));

    // System health details
    utils::JsonObject system_obj;
    system_obj["soc_temperature"] = utils::JsonValue(health.system_health.soc_temperature_celsius);
    system_obj["thermal_throttling"] = utils::JsonValue(health.system_health.thermal_throttling_active);
    system_obj["total_memory_mb"] = utils::JsonValue(static_cast<int>(health.system_health.total_memory_mb));
    system_obj["available_memory_mb"] = utils::JsonValue(static_cast<int>(health.system_health.available_memory_mb));
    system_obj["rtsp_memory_usage_mb"] = utils::JsonValue(static_cast<int>(health.system_health.rtsp_memory_usage_mb));
    system_obj["average_cpu_usage"] = utils::JsonValue(health.system_health.average_cpu_usage);
    system_obj["active_stream_count"] = utils::JsonValue(static_cast<int>(health.system_health.active_stream_count));
    system_obj["is_healthy"] = utils::JsonValue(health.system_health.isHealthy());
    obj["system_health"] = utils::JsonValue(system_obj);

    return utils::JsonValue(obj);
}

utils::JsonValue RTSPHandler::systemStatsToJson(const aibox::rtsp::MultiplexerStatistics& stats) {
    utils::JsonObject obj;

    obj["total_streams_processed"] = utils::JsonValue(static_cast<int64_t>(stats.total_streams_processed));
    obj["total_packets_processed"] = utils::JsonValue(static_cast<int64_t>(stats.total_packets_processed));
    obj["total_bytes_processed"] = utils::JsonValue(static_cast<int64_t>(stats.total_bytes_processed));
    obj["average_processing_time_ms"] = utils::JsonValue(stats.average_processing_time_ms);
    obj["peak_memory_usage_mb"] = utils::JsonValue(static_cast<int>(stats.peak_memory_usage_mb));
    obj["uptime_seconds"] = utils::JsonValue(static_cast<int64_t>(stats.uptime_seconds));

    return utils::JsonValue(obj);
}

// Validation helpers
bool RTSPHandler::validateStreamId(const std::string& stream_id) {
    if (stream_id.empty() || stream_id.length() > 64) {
        return false;
    }

    // Stream ID should contain only alphanumeric characters, hyphens, and underscores
    std::regex valid_pattern("^[a-zA-Z0-9_-]+$");
    return std::regex_match(stream_id, valid_pattern);
}

bool RTSPHandler::validateStreamConfig(const aibox::rtsp::RTSPConnectionConfig& config) {
    // Validate stream ID
    if (!validateStreamId(config.stream_id)) {
        return false;
    }

    // Validate RTSP URL
    if (config.rtsp_url.empty() || config.rtsp_url.find("rtsp://") != 0) {
        return false;
    }

    // Validate timeouts
    if (config.timeout_ms <= 0 || config.timeout_ms > 60000) {
        return false;
    }

    // Validate retry settings
    if (config.retry_count < 0 || config.retry_count > 10) {
        return false;
    }

    return true;
}

bool RTSPHandler::validateControlAction(const std::string& action) {
    return action == "start" || action == "stop" || action == "restart" ||
           action == "connect" || action == "disconnect" || action == "reconnect";
}

// Error handling helpers
utils::JsonValue RTSPHandler::createStreamNotFoundError(const std::string& stream_id) {
    return createErrorResponse("Stream not found: " + stream_id, "STREAM_NOT_FOUND", 404);
}

utils::JsonValue RTSPHandler::createInvalidConfigError(const std::string& details) {
    return createErrorResponse("Invalid configuration: " + details, "INVALID_CONFIG", 400);
}

utils::JsonValue RTSPHandler::createSystemError(const std::string& details) {
    return createErrorResponse("System error: " + details, "SYSTEM_ERROR", 500);
}

} // namespace server::handlers
